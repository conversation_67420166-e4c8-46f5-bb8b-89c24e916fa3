import 'dart:convert';

import 'package:even/common/components/libpomelo/startx.dart';
import 'package:even/model/app.dart';
import 'package:even/model/ble.dart';
import 'package:even/common/services/event/event_svc.dart';
import 'package:even/modules/translate/service/audio/audio_interface.dart';
import 'package:even/modules/translate/service/basic_dst_timer.dart';
import 'package:even/modules/translate/service/content_split.dart';
import 'package:even/modules/translate/service/ex_trans_mic.dart';
import 'package:even/modules/translate/service/trans_statistics.dart';
import 'package:even/modules/translate/service/trans_stucts.dart';
import 'package:even/modules/translate/service/ex_trans_svc_exit.dart';
import 'package:even/service/app.dart';
import 'package:even/service/azure_trans_text.dart';
import 'package:even/service/mic_monitor.dart';
import 'package:even/service/monitor.dart';
import 'package:even/service/proto.dart';
import 'package:even/config/setting.dart';
import 'package:even/modules/translate/service/trans_helper.dart';
import 'package:even/modules/translate/service/trans_model.dart';
import 'package:even/utils/check.dart';
import 'package:even/utils/library.dart';
import 'package:get/get.dart';
import 'package:synchronized/synchronized.dart';
import 'ex_trans_svc_settings.dart';
import 'ex_trans_ios_asr.dart';
import 'translate_basic_timeout_handler.dart';

// import 'package:wakelock_plus/wakelock_plus.dart';
const logTag = "trans";
final _log = logger.getLoggerTag(logTag);

class TransSvc {
  static TransSvc? _instance;

  static TransSvc get get => _instance ??= TransSvc._();

  Rx<TranslateSetting> setting = TranslateSetting().obs;

  final StreamController<TransData> transChange = StreamController.broadcast();
  final StreamController<bool> transLiveChange = StreamController.broadcast();
  final StreamController<MicState> micDebugListener =
      StreamController.broadcast();
  AudioInterface? audio;
  String? _sessionId;

  final Lock _runningLock = Lock();
  final TranslationRetryHandler _timeoutHandler = TranslationRetryHandler();

  Future<bool> isRunning() async {
    return _runningLock.synchronized(() {
      return running.value;
    });
  }

  Timer? _onTransTimer;

  Future<void> setRunning(bool run) async {
    return _runningLock.synchronized(() {
      running.value = run;
      // runningBroadcast.sink.add(run);
    });
  }

  RxBool running = false.obs;
  RxBool isTimeOver = false.obs;

  // StreamController<bool> runningBroadcast = StreamController.broadcast();

  TransData? curTransData = TransData();

  // TransAudio transAudio = TransAudio();
  dynamic _lastItem;

  TransSvc._() {
    onInit();
    // BleManager.get().eventBleReceive.listen((res) async {
    //   if (res.type == "VoiceChunk" &&
    //       _micStartMs != null &&
    //       Setting.get.isDebug) {
    //     _fistPackDelayMs ??= Utils.getTimestampMs() - _micStartMs! - 100;
    //     _fistPackMs ??= Utils.getTimestampMs() - 100;
    //     var seq = res.data[0].toInt();
    //     _seq = _seq + getSeqIncrease(seq);
    //     _lastSeq = seq;
    //     var at = _fistPackMs! + _seq * 100 - 100;
    //     _micDelayMs = Utils.getTimestampMs() - at;
    //     packCountPlus();

    //     micDebugListener.sink.add(
    //       MicState(
    //         fistPackDelayMs: _fistPackDelayMs,
    //         micDelayMs: _micDelayMs,
    //         receiveCount: _receiveCount,
    //         seqSum: _seq,
    //         seq: seq,
    //       ),
    //     );
    //   }
    // });
    transChange.onListen = () {
      transChange.sink.add(curTransData!);
    };
    // runningBroadcast.onListen = () {
    //   runningBroadcast.sink.add(running);
    // }; // _initIsolate();
  }

  bool isSendDstToUpstairs = false;

  // final Lock _onTransLock = Lock();
  final Lock _doTransLock = Lock();
  bool previousIsEnd = false;
  OnTransParams? _lastTransParams;
  final BasicDstTimer _basicDstTimer = BasicDstTimer();

  Future<void> onTrans(String src, String dst, bool isEnd) async {
    bool isNewLine = !isEnd && previousIsEnd;
    if (src.trim().isEmpty) {
      previousIsEnd = isEnd;
      return;
    }
    var rowNo = curTransData!.srcList.length - 1;
    _log.i(
        "_onTrans: isEnd: $isEnd,rowNo: $rowNo,isDstFromNative: $isDstFromNative, isNewLine: $isNewLine, src: $src, dst: $dst");
    _lastTransParams = OnTransParams(
        src: src.trim(), isEnd: isEnd, rowNo: rowNo, isNewLine: isNewLine);
    curTransData!.srcList[curTransData!.srcList.length - 1] = src;
    // if (isDstFromNative) {
    // curTransData!.dstList[curTransData!.dstList.length - 1] = dst;
    // }
    if (isEnd) {
      _addNewLine(curTransData!.srcList);
      _cleanSrcVadMs();
      // if (isDstFromNative) {
      // _addNewLine(curTransData!.dstList);
      // }
    } else {
      insertVad(src, rowNo, mode: setModel);
    }
    if (isDstFromNative && strNoEmpty(dst)) {
      // var sendList = Trans.get.addSendDst2(dst, isEnd);
      // Proto.sendTranslatorDst(sendList.join("\r"));
      // TransSvc.get.addSendDst2(dst.trim(), isEnd);
      // await delaySend(false);
      stableDst(dst.trim(), isEnd, rowNo, isNewLine);
    }
    if (!isDstFromNative && !deafModel) {
      if (setModel == App.modeAsr ||
          setModel == App.asr_mode_translate_ios_offline) {
        if (isEnd) {
          _basicDstTimer.end();
        } else if (isBasicModel) {
          _basicDstTimer.onTrans(src);
        }
      } else if (setModel == App.modeAsrStableDst) {
      } else {
        _log.e("error setModel $setModel");
      }
    }
    if (strNoEmpty(src)) {
      TransSvc.get.addSendSrc2(src.trim(), isEnd, isNewLine);
      await delaySend(true);
    }
    previousIsEnd = isEnd;
    transChange.sink.add(curTransData!);
  }

  ContentSplit contentSplit = ContentSplit();

  void _doBasicTrans() {
    _lastTransParams = contentSplit.onNewSrc(_lastTransParams!);
    doTrans(_lastTransParams!, from, to,
        (src, dst, isEnd2, rowNo2, isNewLine2) async {
      dst = contentSplit.onNewDst(src, dst, isEnd2, rowNo2);
      _log.v(
          "1 doTrans rowNo: ${_lastTransParams!.rowNo} src:${_lastTransParams!.src}  ret: $dst isEnd2: $isEnd2");
      if (!await isRunning()) return;
      await _doTransLock.synchronized(() async {
        _changeLine(curTransData!.dstList, rowNo2, dst);
        _addNewLine(curTransData!.dstList);
        transChange.sink.add(curTransData!);
        await TransSvc.get.addSendDst2(dst.trim(), isEnd2, true);
        // if (_delayPlayDstTimer == null) {
        if (_transModel != TransModel.free) {
          delaySend(false);
        }
      }, timeout: const Duration(seconds: 2)).catchError((dynamic e) {
        _log.e("_doTransLock error: $e");
      });
    });
  }

  void _changeLine(List<String> list, int rowNo, String change) {
    if (list.length <= rowNo) {
      for (var i = 0; i < rowNo - list.length + 1; i++) {
        list.add("");
      }
    }
    list[rowNo] = change;
    _log.i("_changeLine rowNo: $rowNo, change: $change");
  }

  int _addNewLine(List<String> list) {
    if (list.isEmpty || list[list.length - 1].isNotEmpty) {
      list.add("");
    }
    return list.length - 1;
  }

  bool get isDstFromNative =>
      setModel != App.modeAsr &&
      setModel != App.modeAsrStableDst &&
      setModel != App.asr_mode_translate_ios_offline;

  // Timer? _newLineHoldTimer;
  // String _newLineCacheDst = "";
  // void _newLineHold(int rowNo2) {
  //   _newLineHoldTimer ??=
  //       Timer(Duration(milliseconds: Setting.get.newLineDelayMs), () async {
  //     _newLineHoldTimer = null;
  //     if (!await isRunning()) {
  //       return;
  //     }
  //     _changeLine(curTransData!.dstList, rowNo2, _newLineCacheDst);
  //     // _log.i("stableDst no change, dst: $dst");
  //     // TransSvc.get.addSendDst2(dst.trim(), isEnd);
  //     await _setLastCacheDstTimeMs();
  //     _delayPlayDst2(_newLineCacheDst, false, true);
  //     // delaySend(false);
  //     _cacheStableDst = _newLineCacheDst;
  //   });
  // }

  void stableDst(String dst, bool isEnd, int rowNo2, bool isNewLine) async {
    // curTransData!.dstList[curTransData!.dstList.length - 1] = dst;

    if (!isEnd) {
      // if (isNewLine || _newLineHoldTimer != null) {
      //   //如果是新行需要先hold住N秒不显示
      //   _newLineCacheDst = dst;
      //   _newLineHold(rowNo2);
      //   return;
      // }
      // _changeLine(curTransData!.dstList, rowNo2, dst);
      if (_stableDstTimer == null &&
          (_cacheStableDst.isEmpty || dst.contains(_cacheStableDst))) {
        //stableDst no change
        _changeLine(curTransData!.dstList, rowNo2, dst);
        // _log.i("stableDst no change, dst: $dst");
        // TransSvc.get.addSendDst2(dst.trim(), isEnd);
        _setLastCacheDstTimeMs();
        _delayPlayDst2(dst, isEnd, isNewLine);
        // delaySend(false);
        _cacheStableDst = dst;
      } else {
        _log.i("stableDst changed.stable delay send dst: $dst");
        _stableDstSend(rowNo2, dst, isEnd, isNewLine);
      }
    } else {
      // _newLineHoldTimer?.cancel();
      // _newLineHoldTimer = null;
      _changeLine(curTransData!.dstList, rowNo2, dst);
      // _log.i("stableDst end dst: $dst len: ${curTransData!.dstList.length}");
      _addNewLine(curTransData!.dstList);
      _cacheStableDst = "";
      _stableDstTimer?.cancel(); //断句清除定时器
      _stableDstTimer = null;
      _setLastCacheDstTimeMs();
      _delayPlayDst2(dst, isEnd, isNewLine);
      // TransSvc.get.addSendDst2(dst.trim(), isEnd);
      // delaySend(false);
    }
  }

  void _stableDstSend(int rowNo2, String dst, bool isEnd, bool isNewLine) {
    _stableDstTimer ??=
        Timer(Duration(milliseconds: Setting.get.stableDstMs), () async {
      // delaySend(false);
      _setLastCacheDstTimeMs();
      _delayPlayDst2(dst, isEnd, isNewLine);
      _changeLine(curTransData!.dstList, rowNo2, dst);
      _stableDstTimer = null;
      _cacheStableDst = "";
    });
  }

  String _cacheStableDst = "";

  int _lastEndSendDstMs = 0;
  int _lastSendDstMs = 0;

  /// 多少毫秒内只发送一个包
  // final _maxSendDelayMs = 0;
  Timer? _dstDelayTimer, _stableDstTimer, _delaySendTimer;

  // ignore: unused_field
  bool _lastIsSrc = false;

  // bool _hasDst = false;
  // int _lastSendMs = 0;
  var lock = Lock();

//100毫秒内只允许发送一个包，
  Future<void> delaySend(bool isSrc, [bool? isEnd]) async {
    _log.i(
        "delaySend: $isSrc, deafModel: $deafModel, isSendDstToUpstairs: $isSendDstToUpstairs");
    if (!deafModel && isSendDstToUpstairs && isSrc) {
      return;
    }
    await lock.synchronized(() async {
      bool isRunning = await this.isRunning();
      _log.i("[delaySend] isSrc: $isSrc, isRunning: $isRunning");
      if (!isRunning) return;
      _log.i("[delaySend] isSrc: $isSrc, isSending: $isSending");
      if (!isSending) {
        await _timerSendTrans(isSrc, isEnd);
      } else {
        if (isSrc) {
          // _hasSrc = true;
          sendSrc.setHasData(true);
        } else {
          sendDst.setHasData(true);
        }
      }
      transChange.sink.add(curTransData!);
    }, timeout: Duration(seconds: 2));
  }

  String _cacheLastSrc = "";
  String _cacheLastDst = "";

  bool get isSending => sendSrc.sending || sendDst.sending;

  /// 返回是否发送成功, 不需要重发也返回true
  Future<bool> doSend(bool isSrc) async {
    if (!await isRunning()) return true;
    if (!deafModel && isSendDstToUpstairs && isSrc) {
      return true;
    }
    _log.i("doSend  isSrc: $isSrc");
    var ret = true;
    if (isSrc) {
      var send = sendSrc.data.join("\r");
      _log.i("doSend src send: $send");
      if (send != _cacheLastSrc) {
        if (strEmpty(send)) return true;
        sendSrc.setSending(true);
        try {
          ret = await Proto.sendTranslatorSrc(send,
              newScreen: sendSrc.newScreen, pos: sendSrc.pos);
        } catch (e) {
          _log.e("[doSend] sendTranslatorSrc error: $e");
        }

        sendSrc.setSending(false);
        if (ret) {
          _cacheLastSrc = send;
        }
        lastSendSrc = sendSrc.data.toList();
      }
    } else {
      var send = sendDst.data.join("\r");
      _log.i("doSend dst send: $send");
      if (send != _cacheLastDst) {
        if (strEmpty(send)) return true;
        sendDst.setSending(true);
        try {
          ret = await Proto.sendTranslatorDst(send,
              newScreen: sendDst.newScreen, pos: sendDst.pos);
        } catch (e) {
          _log.e("[doSend] sendTranslatorDst error: $e");
        }

        if (isSendDstToUpstairs) {
          try {
            await Proto.sendTranslatorSrc(send,
                newScreen: sendDst.newScreen, pos: sendDst.pos);
          } catch (e) {
            _log.e("[doSend] sendTranslatorSrc error2: $e");
          }
        }
        _log.i("doSend dst ret: $ret");
        sendDst.setSending(false);
        if (ret) {
          _cacheLastDst = send;
        }
        lastSendDst = sendDst.data.toList();
      }
      _log.i("doSend dst _cacheLastDst: $_cacheLastDst");
    }
    // if (!Setting.get.transVadScreen) {}
    _clean(isSrc);
    if (ret) {
      transLiveChange.sink.add(isSrc);
    }
    return ret;
  }

  int _failedCount = 0;
  final int _failedMaxCount = 10;

  Future<void> _timerSendTrans(bool isSrc, [bool? isEnd]) async {
    if (!await isRunning() || !isCanSendData) return;
    _log.i("_timerSendTrans isSrc: $isSrc");
    var sendIsSrc = isSrc;
    if (!_lastIsSrc && sendSrc.hasData) {
      if (!sendIsSrc) {
        sendIsSrc = true;
        // _hasDst = true;
        sendDst.setHasData(true);
      }
    } else if (_lastIsSrc && sendDst.hasData) {
      if (sendIsSrc) {
        sendIsSrc = false;
        sendSrc.setHasData(true);
      }
    }
    if (sendDst.hasData && _transModel == TransModel.free) {
      sendIsSrc = false;
    }

    // if (sendIsSrc) {
    //   _log.i("send src1.1");
    // }
    if (isEnd == true && !sendIsSrc) _lastEndSendDstMs = Utils.getTimestampMs();
    _log.i(
        "_timerSendTrans _lastIsSrc:$_lastIsSrc sendIsSrc: $sendIsSrc , isSrc: $isSrc, hasSrc: ${sendSrc.hasData}, hasDst: ${sendDst.hasData}");
    var isSendSucc =
        await doSend(sendIsSrc).timeout(3.seconds).catchError((dynamic e) {
      _log.e("doSend error2 $e");
      return false;
    });
    _log.i("isSendSucc: $isSendSucc");
    if (isSendSucc) {
      if (sendIsSrc) {
        sendSrc.setHasData(false);
      } else {
        sendDst.setHasData(false);
        _lastSendDstMs = Utils.getTimestampMs();
        if (isEnd == true) {
          _lastEndSendDstMs = _lastSendDstMs;
        }
        // _log.i("_lastSendDstMs set : $_lastSendDstMs ");
      }
      _failedCount = 0;
    } else {
      _failedCount++;
    }

    _lastIsSrc = sendIsSrc;
    // _lastSendMs = Utils.getTimestampMs();
    // _log.i("_lastSendMs: $_lastSendMs");
    if (_failedCount < _failedMaxCount) {
      //最多尝试几次
      if (sendSrc.hasData || sendDst.hasData) {
        var isSrc = sendDst.hasData ? false : true;
        await 50.milliseconds.delay(); //让出CPU，防止递归bug卡死
        _timerSendTrans(isSrc, isEnd);
        return;
      }
    }

    _delaySendTimer = null;
  }

  int? _lastSeq;

  int getSeqIncrease(int seq) {
    //计算debug调试信息
    // _log.i("seq: $seq");
    if (_lastSeq == null) return 1;
    if (seq > _lastSeq!) {
      int diff = seq - _lastSeq!;
      if (diff > 1) {
        _log.e("1.0 lost mic pack $seq");
      }
      return diff;
    } else {
      if (_lastSeq == 255) {
        if (seq == 0) {
          return 1;
        } else {
          _log.e("1.1 lost mic pack $seq");
          return seq + 1;
        }
      }
      if (seq == _lastSeq) {
        _log.e("1.11 lost mic pack $seq  _lastSeq: $_lastSeq");
        return 0;
      }
      _log.e("1.2 lost mic pack $seq  _lastSeq: $_lastSeq");
      return seq + 1 + (255 - _lastSeq!);
    }
  }

  final _lock = Lock();

  void packCountPlus() async {
    await _lock.synchronized(() {
      _receiveCount++;
    });
  }

  double get languagePanelH => 162.h;
  double? panelTop;
  double? cacheTop;
  double? panelBottom = 0;
  double? parentH;
  double? panelH;
  double? detailsBtnPanelH;
  bool visibleLangPanel = true;

  bool isCanSendData = true;

  int? _micStartMs;
  int? _fistPackMs;

  int? _fistPackDelayMs;
  int _micDelayMs = 0;
  int _receiveCount = 0;
  int _seq = 0;

  int maxLine = 5;

  TransPack sendSrc = TransPack();
  List<String> lastSendSrc = [];
  List<String> lastSendDst = [];
  List<String> _cacheSrc = [];
  TransPack sendDst = TransPack();
  List<String> _cacheDst = [];
  final List<String> _overflowQueueDst = [];

  // final int _delayPlayMs = 600; // 短句后翻译 播放速度
  Timer? _delayPlayDstTimer;

  void testSend() {
    // _delayPlayDstTimer?.cancel();
    // _delayPlayDstTimer =null;
    if (_delayPlayDstTimer == null) {
      delaySend(false);
      _delayPlayDstTimer = Timer.periodic(
          Duration(milliseconds: Setting.get.dstPlayMs), _delayPlay);
    }
    // listener.sink.add(null);
  }

  Future<void> Function(bool isOk)? _cacheDstQueueCB;
  bool _delayPlayRun = false;

  void _delayPlay(Timer timer) async {
    if (_delayPlayRun) return;
    _delayPlayRun = true;
    if (!await isRunning()) {
      _dstDelayTimer?.cancel();
      _dstDelayTimer = null;
      _delayPlayRun = false;
      return;
    }
    // _log.i("_delayPlay");
    // await _playDstLock.synchronized(() {
    // });
    if (_overflowQueueDst.isEmpty) {
      await _cacheDstQueueCB?.call(true);
      _delayPlayRun = false;
      return;
    }
    var row = _overflowQueueDst.removeAt(0);
    _log.i('send _delayPlay: $row');

    sendDst.data.add(row);
    if (sendDst.data.length > maxLine) {
      sendDst.data = sendDst.data.sublist(sendDst.data.length - maxLine);
    }
    // listener.sink.add(null);
    await delaySend(false);
    // if (_cacheQueueDst.isEmpty) {
    //   _cacheDstQueueCB?.call(true);
    // }
    _delayPlayRun = false;
  }

  void _cleanSrc() {
    sendSrc.clear();
    lastSendSrc.clear();
  }

  void _cleanDst() {
    _log.i("_cleanDst");
    lastSendDst.clear();
    sendDst.data.clear();
  }

  Timer? _queueUnsendTimer;

  void _setCacheQueueUnsendTimer([int? milliseconds]) {
    _log.i(
        "_delayPlayDst2-> _setCacheQueueUnsendTimer ${milliseconds ?? Setting.get.dstPlayMs} isSuc: ${_queueUnsendTimer == null}");
    _queueUnsendTimer ??=
        Timer(Duration(milliseconds: milliseconds ?? Setting.get.dstPlayMs),
            () async {
      _log.i("run CacheQueueUnsendTimer");
      if (_cacheQueueUnsend.isEmpty) {
        _queueUnsendTimer = null;
        return;
      }

      if (_lastSendDstMs > 0) {
        // var lastSendRows = _lastSendRows > maxLine ? 1 : _lastSendRows;
        var lastSendRows = 1;
        var needWaitMs = Setting.get.dstPlayMs * lastSendRows;
        var now = Utils.getTimestampMs();
        var diffMs = now - _lastSendDstMs;
        if (diffMs < needWaitMs) {
          _log.i(
              "_delayPlay sleep ${needWaitMs - diffMs} ms needWaitMs: $needWaitMs , $now - $_lastSendDstMs ");
          await Future.delayed(
              Duration(milliseconds: needWaitMs - diffMs), () => null);
        }
      }

      var send = _cacheQueueUnsend.removeAt(0);
      _cleanDst();
      // _queueUnsendTimer = null;
      _delayPlayDst2(send, true, false, true);
    });
  }

  // int _lastSendRows = 0;

  ///  断句刷屏也不能太快，
  Future<List<String>> _delayPlayDst2(String send, bool isEnd, bool isNewLine,
      [bool isJump = false]) async {
    _log.i(
        "_delayPlayDst2 isJump:$isJump isNewLine:$isNewLine isEnd: $isEnd isNewline: $isNewLine,send: $send");
    return await _playDstLock.synchronized(() {
      if (Setting.get.transVadScreen) {
        _log.i(
            "_delayPlayDst2-> in isJump:$isJump isNewLine:$isNewLine isEnd: $isEnd isNewline: $isNewLine,send: $send");
        //   if (_cacheQueueUnsend.isEmpty || isJump) {
        //     //发送队列是空的
        //     if (_lastEndSendDstMs > 0 && isNewLine && !isJump) {
        //       //计算从上次断句到现在是否发送太快
        //       // var lastSendRows = _lastSendRows > maxLine ? 1 : _lastSendRows;
        //       var lastSendRows = 1;
        //       var needWaitMs = Setting.get.dstPlayMs * lastSendRows;
        //       var now = Utils.getTimestampMs();
        //       var diffMs = now - _lastEndSendDstMs;
        //       if (diffMs < needWaitMs) {
        //         //发送太快了，休息一下
        //         _cacheQueueUnsend.add(send);
        //         _setCacheQueueUnsendTimer(needWaitMs - diffMs);
        //         return sendDst.data;
        //       }
        //     }
        _cleanDst();
        //     _queueUnsendTimer = null;
        //     if (_cacheQueueUnsend.isNotEmpty) {
        //       _setCacheQueueUnsendTimer();
        //     }
        //   } else {
        //     //缓存还没播放完
        //     _log.i(
        //         "_delayPlayDst2-> _缓存还没播放完,cacheLen:${_cacheQueueUnsend.length} ,${jsonEncode(_cacheQueueUnsend)} new: $send");
        //     if (isNewLine || _cacheQueueUnsend.isEmpty) {
        //       _cacheQueueUnsend.add(send);
        //     } else if (_cacheQueueUnsend.isNotEmpty) {
        //       _cacheQueueUnsend[_cacheQueueUnsend.length - 1] = send;
        //     }
        //     if (_queueUnsendTimer == null) {
        //       _setCacheQueueUnsendTimer();
        //     } else {
        //       _log.i(
        //           "_delayPlayDst2->_缓存的缓存还没播放完_V2,cacheLen:${_cacheQueueUnsend.length} ,${jsonEncode(_cacheQueueUnsend)},new: $send");
        //     }
        //     return sendDst.data;
        //   }
      }
      if (_cacheDst.length > sendDst.data.length) {
        sendDst.data = _cacheDst.sublist(0, sendDst.data.length);
      } else {
        sendDst.data =
            sendDst.data.sublist(0, sendDst.data.length - _cacheDst.length);
      }
      _cacheDst = UiUtils.measureStringList(send, textStyle: textStyle);
      if (Setting.get.transVadScreen) {
        _cacheDst.insert(0, " ");
        _cacheDst.insert(0, " ");
      } else if (isNewLine) {
        sendDst.data.add(" ");
      }

      // _lastSendRows = _cacheDst.length;
      sendDst.data.addAll(_cacheDst);
      if (sendDst.data.length > maxLine) {
        sendDst.data = sendDst.data.sublist(sendDst.data.length - maxLine);
      }
      if (isEnd) {
        _cacheDst.clear();
      }
      if (isNewLine) {
        sendDst.newScreen = 2;
        sendDst.pos = 0;
      } else {
        var (animal, pos) =
            TransHelper.getAnimal(lastSendDst, sendDst.data, maxLine);
        sendDst.newScreen = animal;
        sendDst.pos = pos;
      }
      delaySend(false, isEnd);

      _log.i(
          "_cacheQueueUnsend:${_cacheQueueUnsend.length}, sendDst: ${jsonEncode(sendDst)}");
      // if (sendDst2.length > maxLine) {
      //   sendDst2 = sendDst2.sublist(sendDst2.length - maxLine);
      // }
      return sendDst.data;
    });
  }

  final List<String> _cacheQueueUnsend = [];
  final Lock _playDstLock = Lock();

  void _setCacheDstQueueCB() {
    _cacheDstQueueCB = (isOk) async {
      if (_cacheQueueUnsend.isEmpty) {
        _cacheDstQueueCB = null;
        return;
      }

      if (_lastSendDstMs > 0) {
        var lastSendRows = lastSendDst.length;
        var needWaitMs = Setting.get.dstPlayMs * lastSendRows;
        var now = Utils.getTimestampMs();
        var diffMs = now - _lastSendDstMs;
        if (diffMs < needWaitMs) {
          _log.i(
              "_delayPlay sleep ${needWaitMs - diffMs} ms needWaitMs: $needWaitMs , $now - $_lastSendDstMs ");
          await Future.delayed(
              Duration(milliseconds: needWaitMs - diffMs), () => null);
        }
      }

      var send = _cacheQueueUnsend.removeAt(0);
      _cacheDstQueueCB = null;
      _delayPlayDst(send);
    };
  }

  List<String> _delayPlayDst(String send) {
    // return await _playDstLock.synchronized(() async {
    // });
    if (Setting.get.transVadScreen) {
      if (_overflowQueueDst.isEmpty && _cacheDstQueueCB == null) {
        if (_lastSendDstMs > 0) {
          var lastSendRows = lastSendDst.length;
          var needWaitMs = Setting.get.dstPlayMs * lastSendRows;
          var now = Utils.getTimestampMs();
          var diffMs = now - _lastSendDstMs;
          if (diffMs < needWaitMs) {
            _cacheQueueUnsend.add(send);
            _setCacheDstQueueCB();
            return sendDst.data;
          }
        }
        _cleanDst();
        if (_cacheQueueUnsend.isNotEmpty) {
          _setCacheDstQueueCB();
        }
      } else {
        //缓存还没播放完
        _log.i(
            "_delayPlayDst->_缓存还没播放完,cacheLen:${_overflowQueueDst.length} new: $send");
        _cacheQueueUnsend.add(send);
        if (_cacheDstQueueCB == null) {
          _setCacheDstQueueCB();
        } else {
          _log.i(
              "_delayPlayDst->_缓存的缓存还没播放完_V2,cacheLen:${_overflowQueueDst.length} new: $send");
        }
        return sendDst.data;
      }
    }
    var tmp = UiUtils.measureStringList(send, textStyle: textStyle);
    if (Setting.get.transVadScreen) {
      tmp.insert(0, " ");
      tmp.insert(0, " ");
    }

    if (tmp.length > maxLine) {
      if (sendDst.data.isEmpty) {
        var temp = tmp.sublist(0, maxLine);
        sendDst.data.addAll(temp);
        delaySend(false);
        tmp = tmp.sublist(maxLine);
        _overflowQueueDst.addAll(tmp);
      } else {
        _overflowQueueDst.addAll(tmp);
      }
    } else {
      if (sendDst.data.isEmpty) {
        sendDst.data.addAll(tmp);
        delaySend(false);
      } else {
        _overflowQueueDst.addAll(tmp);
      }
    }
    _log.i(
        "_cacheQueueDst:${_overflowQueueDst.length}, ${jsonEncode(_overflowQueueDst)},sendDst: ${jsonEncode(sendDst)}");
    // if (sendDst2.length > maxLine) {
    //   sendDst2 = sendDst2.sublist(sendDst2.length - maxLine);
    // }
    return sendDst.data;
  }

  // ignore: unused_field
  int _lastCacheDstTimeMs = 0;

  // final Lock _lastCacheDstLock = Lock();
  void _setLastCacheDstTimeMs() {
    _lastCacheDstTimeMs = Utils.getTimestampMs();
    // await _lastCacheDstLock.synchronized(() {
    // });
  }

  // Future<int> _getLastCacheDstTimeMs() async {
  //   return await _lastCacheDstLock.synchronized(() {
  //     return _lastCacheDstTimeMs;
  //   });
  // }

  Future<List<String>> addSendDst2(
      String send, bool isEnd, bool isNewLine) async {
    if (isEnd) {
      _log.i("addSendDst2 send: $send  --end");
    }
    _setLastCacheDstTimeMs();
    if (setModel == App.modeAsr ||
        setModel == App.asr_mode_translate_ios_offline) {
      //短句后翻译 需要特殊处理
      return _delayPlayDst2(send, isEnd, isNewLine);
    }
    if (Setting.get.transVadScreen) {
      _cleanDst();
    } else if (isNewLine) {
      sendDst.data.add("");
    }
    //-- 清除上一次发送的未断句数据
    if (_cacheDst.length > sendDst.data.length) {
      sendDst.data = _cacheDst.sublist(0, sendDst.data.length);
    } else {
      sendDst.data =
          sendDst.data.sublist(0, sendDst.data.length - _cacheDst.length);
    }
    //--end 清除上一次发送的未断句数据

    _cacheDst = UiUtils.measureStringList(send, textStyle: textStyle);
    if (Setting.get.transVadScreen) {
      _cacheDst.insert(0, " ");
      _cacheDst.insert(0, " ");
    }
    sendDst.data.addAll(_cacheDst);
    if (sendDst.data.length > maxLine) {
      sendDst.data = sendDst.data.sublist(sendDst.data.length - maxLine);
    }
    if (isEnd) {
      _cacheDst.clear();
    }
    return sendDst.data;
  }

  Timer? _cleanSrcTimer, _cleanDstTimer;

  // 翻译清屏
  void _clean(bool isSrc) {
    _log.i("11_clean: $isSrc");
    _cleanSrcTimer?.cancel();
    if (!isCanSendData) {
      return;
    }
    _cleanSrcTimer = Timer(const Duration(seconds: 6), () async {
      if (!await isRunning() ||
          !BleManager.isBothConnected() ||
          !isCanSendData) {
        // timer.cancel();
        return;
      }
      _cleanSrc();
      sendSrc.data.add(" ");
      // delaySend(true);
      if (!_timeoutHandler.isShowingWeakToast) {
        Proto.sendTranslatorSrc("\r", newScreen: 0, pos: 0, timeoutMs: 1000);
      }
      transLiveChange.sink.add(true);
    });
    _cleanDstTimer?.cancel();
    // _log.i("11_clean 221: $isSrc");
    _cleanDstTimer = Timer(const Duration(seconds: 6), () async {
      // _log.i("11_clean 22: $isSrc");

      if (!await isRunning() ||
          !BleManager.isBothConnected() ||
          !isCanSendData) {
        // timer.cancel();
        return;
      }
      _cleanDst();
      sendDst.data.add(" ");
      if (!_timeoutHandler.isShowingWeakToast) {
        Proto.sendTranslatorDst("\r", newScreen: 0, pos: 0, timeoutMs: 1000);
      }
      // var ms = await _getLastCacheDstTimeMs();
      // if (Utils.getTimestampMs() - ms < 6) {
      // }
      transLiveChange.sink.add(false);
    });
    // if (isSrc) {
    // } else {}
    // listener.sink.add(null);
  }

  int _enoughEmptyLineCount(List<String> list) {
    var emptyRowCount = 0;
    for (var i = list.length - 1; i >= 0; i--) {
      var row = list[i];
      if (strEmpty(row)) {
        emptyRowCount++;
      } else {
        break;
      }
    }
    return emptyRowCount;
  }

  bool _isEnoughEmptyLine(List<String> list) {
    var emptyRowCount = 0;
    for (var i = list.length - 1; i >= 0; i--) {
      var row = list[i];
      if (strEmpty(row)) {
        emptyRowCount++;
        if (emptyRowCount > maxLine) {
          return true;
        }
      }
    }
    return false;
  }

  List<String> addSendSrc2(String send, bool isEnd, bool isNewLine) {
    if (Setting.get.transVadScreen) {
      _cleanSrc();
    }

    //-- 清除上一次发送的未断句的数据
    if (_cacheSrc.length > sendSrc.data.length) {
      //不清屏的滚动效果
      sendSrc.data = _cacheSrc.sublist(0, sendSrc.data.length);
    } else {
      sendSrc.data =
          sendSrc.data.sublist(0, sendSrc.data.length - _cacheSrc.length);
    }
    //-- end 清除上一次发送的数据

    // _log.i("111sendSrc2: ${sendSrc2.length}, ${sendSrc2.join(" | ")}    _cacheSrc2: ${_cacheSrc2.join(" | ")}");
    _cacheSrc = UiUtils.measureStringList(send, textStyle: textStyle);
    if (Setting.get.transVadScreen) {
      _cacheSrc.insert(0, " ");
      _cacheSrc.insert(0, " ");
    } else if (isNewLine) {
      sendSrc.data.add("");
    }
    sendSrc.data.addAll(_cacheSrc);
    // _log.i("22sendSrc2: ${sendSrc2.length}, ${sendSrc2.join(" | ")}");
    if (sendSrc.data.length > maxLine) {
      //超过屏幕最大行，只显示最新的maxLine
      sendSrc.data = sendSrc.data.sublist(sendSrc.data.length - maxLine);
    }
    if (isEnd) {
      _cacheSrc.clear();
    }
    if (isNewLine) {
      sendSrc.newScreen = 2;
      sendSrc.pos = 0;
    } else {
      var (animal, pos) =
          TransHelper.getAnimal(lastSendSrc, sendSrc.data, maxLine);
      sendSrc.newScreen = animal;
      sendSrc.pos = pos;
      _log.i(
          "getAnimal newScreen: $animal, pos: $pos, lastSendSrc: $lastSendSrc, sendSrc.data: ${sendSrc.data}");
    }
    // _log.i("33sendSrc2: ${sendSrc2.length}, ${sendSrc2.join(" | ")}");
    return sendSrc.data;
  }

  void clear() {
    contentSplit.clean();
    _subListenResult?.cancel();
    _cacheLastSrc = "";
    _cacheLastDst = "";
    isCanSendData = true;
    _basicDstTimer.callback = _doBasicTrans;
    deafModel = false;
    _queueUnsendTimer?.cancel();
    _queueUnsendTimer = null;
    _cacheQueueUnsend.clear();
    _delayPlayDstTimer?.cancel();
    _delayPlayDstTimer = null;
    _stableDstTimer?.cancel();
    _stableDstTimer = null;
    sendSrc.clear();
    sendDst.clear();
    _cacheDst.clear();
    _cacheSrc.clear();
    _overflowQueueDst.clear();
    _dstDelayTimer = null;
    _delaySendTimer?.cancel();
    _delaySendTimer = null;
    _insertNo.clear();
  }

  StreamSubscription<BleBothDevice>? sub;

  void _listenConnect([bool isClean = false]) {
    sub ??= BleManager.get().curGlassStream.stream.listen((event) async {
      final isConnected = BleManager.isBothConnected();
      bleConnectStatus.value = isConnected
          ? ConnectionStatus.connected
          : ConnectionStatus.disconnected;
    });
    if (isClean) {
      sub?.cancel();
      sub = null;
    }
  }

  String from = "zh-CN";
  String to = "en-US";

  // bool isTransOpen = false;
  RxBool isTransOpen = RxBool(false);
  RxBool hasExited = RxBool(false);
  // 监听蓝牙连接状态
  final bleConnectStatus = Rx<ConnectionStatus>(ConnectionStatus.disconnected);
  final micMonitor = MicMonitor();
  TransModel _transModel = TransModel.free;

  //盲人转写模式
  bool deafModel = false;
  String? transApiKey;
  String? transRegion;
  String setModel = Setting.get.transMode;

  bool get isBasicModel => _transModel == TransModel.free;

  /// 返回 error  成功返回 空字符串
  Future<String> startTrans(
    String from,
    String to, {
    String? mode,
    required bool isPause,
    required TransModel transModel,
    required FuncConfs funcConf,
  }) async {
    _log.i("startTrans from: $from , to: $to, transModel: $transModel");
    var busyStr = "window_content_close_current_feature_to_start".tr;
    _timeoutHandler.reset();
    // if (!isTransOpen.value) {
    //   final result = await FeatureCoordinatorService.to.requestStart();
    //   if (!result) {
    //     _log.e("canRunTranslation false");
    //     return busyStr;
    //   }
    // }
    _transModel = transModel;
    _sessionId ??= "${Utils.getTimestampMs()}";
    // transAudio.startListenMic();
    var diff = Utils.getTimestampMs() - _lastStopMs;
    if (diff < 5000) {
      await Future.delayed(Duration(milliseconds: 5000 - diff), () => null);
    }
    _log.i("startTrans isPause: $isPause");
    _funcStartTimeS = Utils.getTimestampSecond();

    //if (PlatformUtils.isAndroid) {

    transApiKey = funcConf.translator?.apiKey;
    transRegion = funcConf.translator?.region;
    var speechApiKey = funcConf.speech?.apiKey;
    var speechRegion = funcConf.speech?.region;
    // }
    _listenConnect();
    _log.i("startTrans setRunning true");
    await setRunning(true);
    _log.i("startTrans => end setRunning true");
    _fistPackDelayMs = null;
    _fistPackMs = null;
    _receiveCount = 0;
    _seq = 0;
    _lastSeq = null;
    clear();
    this.from = from;
    this.to = to;

    if (from == to) {
      // 听障转写模式
      deafModel = true;
      _transModel = TransModel.free;
    }
    if (deafModel || _transModel == TransModel.free) {
      mode = App.modeAsr;
      if (PlatformUtils.isIOS && Setting.get.isOfflineSpeechIOS) {
        mode = App.asr_mode_translate_ios_offline;
      }
    }
    if (_transModel == TransModel.free) {
      App.get.setCurFunc(Func.translateFree);
    } else {
      App.get.setCurFunc(Func.translate);
    }
    setModel = mode ?? Setting.get.transMode;
    String asrFrom = from;
    if (asrFrom.contains("en") && App.get.appInfo.getRegion == "GB") {
      asrFrom = "en-GB";
    }
    var transTo = to;
    _log.i("startTrans from: $from , to: $to, transModel: $transModel");
    var asrState = await App.get.startAsr(
        mode: setModel,
        asrFrom: asrFrom,
        transTo: transTo,
        sst: Setting.get.transSST,
        apiKey: speechApiKey,
        region: speechRegion,
        sessionId: _sessionId);
    _log.i("startTrans: asrState: $asrState");
    if (asrState['state'] != "sessionStarted") {
      _log.e("startTrans error: $asrState");
      return "window_content_network_error".tr;
    }

    _log.i("startTrans send state");
    var isSucc = await Proto.sendTranslatorState(
        0,
        Glass.languageMap[Glass.fromLanguageMap[from]!["code"]]!,
        Glass.languageMap[Glass.toLanguageMap[to]!["code"]]!);
    if (!isSucc) {
      Toast.show("translator state error 1");
      _log.e("translator state error ");
    }
    _log.i("startTrans =》 end send state");

    if (!isPause) {
      // final (micStartMs, isStartSucc) = await Proto.micOn(
      //   lr: Proto.lR(),
      // );
      var isStartSucc = await micOn(mode: setModel);
      if (!isStartSucc) {
        _log.e("start mic error");
        _listenConnect(true);
        await setRunning(false);
        await App.get.stopAsr(mode: setModel,isNeedDelayOnIos: true,);
        await exit();
        if (setting.value.voiceInput == VoiceInput.phone) {
          return "window_content_microphone_permission".tr;
        }
        return "$busyStr";
      }
      _micStartMs = Utils.getTimestampMs();
    } else {
      _micStartMs = Utils.getTimestampMs();
    }
    await _sendStartTrans();
    _log.i("setting.value.voiceInput: ${setting.value.voiceInput}");
    if (setting.value.voiceInput == VoiceInput.glasses) {
      var isMicSucc = await micMonitor.startMonitoring();
      if (!isMicSucc) {
        _log.e("mic start error");
        await exit();
        return "$busyStr";
      }
    }
    isTransOpen.value = true;
    micMonitor.startMicRun((ok) async {
      if (await isRunning()) {
        logger.d("检测麦克风数据异常，主动关闭翻译");
        await exit();
        App.get.backToHome();
      }
    });
    _startListenResult();
    TransStatistics.get.start();
    if (_transModel == TransModel.free) {
      App.get.setCurFunc(Func.translateFree);
    } else {
      App.get.setCurFunc(Func.translate);
    }
    await Monitor.get.online();
    _onStart(
      from: from,
      asrFrom: asrFrom,
      transTo: transTo,
      speechRegion: speechRegion ?? "",
      transModel: _transModel,
    );
    // WakelockPlus.enable();
    _log.i("startTrans end");
    return "";
  }

  StreamSubscription<dynamic>? _subListenResult;

  void _startListenResult() {
    _subListenResult?.cancel();
    _subListenResult = App.get.eventTranslator.listen((item) async {
      // print("eventTranslator.listen-------${item}");
      if (!await isRunning() || _sessionId != item["sessionId"]) {
        _log.e("already stop. item: $item, _sessionId:$_sessionId");
        return;
      }
      _lastItem = item;
      var isEnd = item["isEnd"] as bool;
      _log.i("subTrans: ${jsonEncode(item)}");
      void doTransProcess(dynamic item) {
        if (isOfflineSpeechIOS) {
          onIosTrans(item);
        } else {
          var src = item["src"] as String;
          var dst = item["dst"] as String;
          var isEnd = item["isEnd"] as bool;
          onTrans(src, dst, isEnd);
        }
      }

      if (isEnd) {
        _onTransTimer?.cancel();
        _onTransTimer = null;
        doTransProcess(_lastItem);
        return;
      }
      _onTransTimer ??=
          Timer(Duration(milliseconds: Setting.get.lessPackDelayMs), () {
        _onTransTimer = null;
        doTransProcess(_lastItem);
      });
    });
  }

  EventSvc? event;

  void _onStart({
    required String from,
    required String asrFrom,
    required String transTo,
    required String speechRegion,
    required TransModel transModel,
  }) {
    hasExited.value = false;
    event = EventSvc();
    event?.startEventTimer(Event.start(
      action: EventAction.translate,
      params: {
        "from": asrFrom,
        "to": transTo,
        "region": speechRegion,
        "model": transModel.name,
        "headUpDisplay": setting.value.headUpDisplay?.name,
      },
    ));
    onSetStart(from: from);
    onBalanceStart();
  }

  void _onStop() {
    onSetEnd();
    onBalanceStop();
    event?.lastEvent?.params?["charCount"] =
        TransStatistics.get.cacheTranslateCharCount;
    event?.lastEvent?.params?["useTimeSeconds"] =
        TransStatistics.get.cacheUseTimeSeconds;
    event?.stopEventTimer();
  }

  Future<void> _sendStartTrans() async {
    if (!Setting.get.transVadScreen) {
      // sendDst.data.insert(0, " ");
      sendDst.data.insert(0, " ");
      // sendSrc.data.insert(0, " ");
      sendSrc.data.insert(0, " ");
    }
    var isSuccSrc = await Proto.sendTranslatorSrc("\r", newScreen: 0, pos: 0);
    if (!isSuccSrc) {
      await Proto.sendTranslatorSrc("\r",
          newScreen: 0, pos: 0, timeoutMs: 1000);
    }
    if (!deafModel) {
      var isSuccDst = await Proto.sendTranslatorDst("\r", newScreen: 0, pos: 0);
      transLiveChange.sink.add(true);
      if (!isSuccDst) {
        await Proto.sendTranslatorDst("\r",
            newScreen: 0, pos: 0, timeoutMs: 1000);
      }
      _cleanDst();
    }
    _cleanSrc();
    transLiveChange.sink.add(false);
  }

  int _lastStopMs = 0;
  int _funcStartTimeS = 0;

  Future<void> _stopOnline() async {
    var funcUseTimeS = Utils.getTimestampSecond() - _funcStartTimeS;
    App.get.setCurFunc(Func.empty);
    if (_transModel == TransModel.free) {
      await Monitor.get
          .online(preFunc: Func.translateFree.name, preFuncTimeS: funcUseTimeS);
    } else {
      await Monitor.get
          .online(preFunc: Func.translate.name, preFuncTimeS: funcUseTimeS);
    }
  }

  /// 暂停翻译，没有退出
  Future<void> stopTrans({String? mode, bool? isPause}) async {
    _log.i("stopTrans from $from to $to");
    //涉及ASR的接口调用，mode不可为空
    mode ??= setModel;
    _timeoutHandler.reset();
    TransStatistics.get.stop();
    await setRunning(false);
    await _stopOnline();
    _onStop();
    // WakelockPlus.disable();
    var isSucc = await Proto.sendTranslatorState(
        1,
        Glass.languageMap[Glass.fromLanguageMap[from]!["code"]]!,
        Glass.languageMap[Glass.toLanguageMap[to]!["code"]]!);
    if (!isSucc) {
      Toast.show("translator state stop error ");
      _log.e("translator state stop error ");
    }
    await App.get.stopAsr(mode: mode, isNeedDelayOnIos: true);
    // var mode2 = mode ?? Setting.get.transMode;
    if (isPause != true) {}
    _syncSrcDstLen();
    clear();
    micMonitor.stopMicRun();
    _lastStopMs = Utils.getTimestampMs();
    _log.i("stopTrans isPause: $isPause");
    transLiveChange.sink.add(true);
  }

  // Future<void> closeMic([String? mode]) async {
  //   for (var i = 0; i < 5; i++) {
  //     var ret = await Proto.micOff(
  //       Proto.lR(),
  //       mode,
  //     );
  //     if (!ret.isTimeout &&
  //         (ret.data[2].toInt() == 0xc9 || ret.data[1].toInt() == 0xc9)) {
  //       break;
  //     } else {
  //       if (running.value || Setting.get.phoneMic) {
  //         break;
  //       }
  //       _log.e("lost pack mic off ${ret.hexStringData()}");
  //     }
  //   }
  // }

  void _syncSrcDstLen() {
    if (curTransData == null) return;
    if (curTransData!.srcList.isNotEmpty &&
        curTransData!.dstList.isNotEmpty &&
        curTransData!.srcList.length > curTransData!.dstList.length) {
      var diffLen = curTransData!.dstList.length - curTransData!.srcList.length;
      for (var i = 0; i < diffLen; i++) {
        curTransData!.dstList.add("");
      }
    }
  }

  Future<void> exit() async {
    logger.d("translate exit");
    await stopRunning();
    _sessionId = null;
    if (!hasExited.value) {
      await doExit();
    } else {
      _log.i(" exit hasExited");
    }
    isTransOpen.value = false;
  }

  Future<void> stopRunning() async {
    if (await isRunning()) {
      await App.get.stopAsr(mode:setModel,isNeedDelayOnIos: true);
      TransStatistics.get.stop();
      await _stopOnline();
      GlobalService.to.getUserInfo(update: true);
      _onStop();
    } else {
      GlobalService.to.getUserInfo(update: true);
    }
    await setRunning(false);
  }

  Future<void> doExit([bool isFromGlass = false]) async {
    _log.i("doExit $isFromGlass");
    isTransOpen.value = false;
    hasExited.value = true;
    _listenConnect(true);
    _timeoutHandler.reset();
    await stopRunning();
    await micOff();
    if (!isFromGlass) {
      await 500.milliseconds.delay();
      await Proto.exit();
    }
    micMonitor.stopMicRun();
    App.get.setCurFunc(Func.empty);
    _lastStopMs = Utils.getTimestampMs();
    curTransData = TransData();
    transChange.sink.add(curTransData!);
    // transAudio.stop();
  }

  int _reqCode = 0;

  void doTrans(OnTransParams lastTransParams, String from, String to,
      TransCallback cb) async {
    _reqCode++;
    var azure = AzureTranslatorText(
      code: _reqCode,
      isEnd: lastTransParams.isEnd,
      rowNo: lastTransParams.rowNo,
      resourceKey: transApiKey!,
      region: transRegion!,
    );
    TransStatistics.get.onTranslate(lastTransParams.src);
    _log.v("2 doTrans rowNo: ${lastTransParams.rowNo}");
    final ret = await _timeoutHandler.executeWithRetry(
      () => azure.post(lastTransParams.src, from, to),
    );
    cb(lastTransParams.src, ret!.dst, ret.isEnd, lastTransParams.rowNo,
        lastTransParams.isNewLine);
  }

  final Map<int, int> _insertNo = {};
  int? _cacheStartSrcMs;

  // final Lock _srcMs = Lock();
  Future<void> _cleanSrcVadMs() async {
    // await _srcMs.synchronized(() {
    // });
    _cacheStartSrcMs = null;
  }

  Future<void> insertVad(String src, int rowNo,{required String mode}) async {
    // await _srcMs.synchronized(() {
    // });
    if (_transModel != TransModel.free) return;
    if (_cacheStartSrcMs != null) {
      var now = Utils.getTimestampMs();
      var diff = (now - _cacheStartSrcMs!) ~/ 1000;
      if (Setting.get.vadMaxS > 3 && diff >= Setting.get.vadMaxS) {
        var vad = (Setting.get.transSST * 5).toInt();
        if (vad > 1000) vad = 1000;
        _log.i(
            "asrInsertEmpty src: $src ,${now - _cacheStartSrcMs!} diff:$diff max: ${Setting.get.vadMaxS},now:$now,_cacheStartSrcMs:$_cacheStartSrcMs ");
        App.get.asrInsertEmpty(vad,mode: mode);
        _insertNo[rowNo] = 1;
      }
    } else {
      _cacheStartSrcMs = Utils.getTimestampMs();
      _log.i("asrInsertEmpty _cacheStartSrcMs: $_cacheStartSrcMs ");
    }

    // return;
    // if (_insertNo.containsKey(rowNo)) {
    //   _insertNo[rowNo] = _insertNo[rowNo]! + 1;
    // } else {
    //   // _log.i("src1111 ${src.length}, ${Setting.get.vadMax}");
    //   if (src.length >= Setting.get.vadMax) {
    //     var vad = (Setting.get.transSST * 2.5).toInt();
    //     if (vad > 500) vad = 500;
    //     _log.i(
    //         "asrInsertEmpty src: $src ,${src.length} max: ${Setting.get.vadMax}");
    //     App.get.asrInsertEmpty(vad);
    //     _insertNo[rowNo] = 1;
    //   }
    // }
  }

  TextStyle get textStyle {
    return const TextStyle(fontSize: UiUtils.fontSize).glassFont;
  }
}

class TransData {
  int id = 0;
  List<String> srcList = [];
  List<String> dstList = [];
  bool isShowSrc = true;
  int createAt = Utils.getTimestampSecond();

  TransData() {
    srcList.add("");
    dstList.add("");
  }
// TransData.from(TransRecordData data) {
//   List<dynamic> list = jsonDecode(data.srcList);
//   srcList = list.map((e) => e.toString()).toList();
//   list = jsonDecode(data.dstList);
//   dstList = list.map((e) => e.toString()).toList();
//   isShowSrc = data.showSrc;
//   id = data.id;
//   createAt = data.createAt;
// }
}

class MicState {
  int? fistPackDelayMs;
  int micDelayMs = 0;
  int receiveCount = 0;
  int seq = 0;
  int seqSum = 0;

  MicState({
    this.fistPackDelayMs,
    this.micDelayMs = 0,
    this.receiveCount = 0,
    this.seq = 0,
    this.seqSum = 0,
  });
}
